<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { getPretrainList, strtPretrain, deleteMode, reName } from '@/api/avatarChat';
  import { getLocalItem } from '@/utils/common';
  import Loading from '@/components/Loading/index.vue';
  import Icon from '@/components/Icon/index.vue';
  import EmptyImage from '@/assets/image/base/pictures/empty_project.png';
  import AVATAR01 from '@/assets/image/avatar/2d.png';
  import CreateDigitalHumanModal from '../Create/index.vue';

  const defaultCharacterLibrary = [
    {
      id: 0,
      created_at: '2025-03-27T14:40:39.184567+08:00',
      updated_at: '2025-03-27T14:46:07.849233+08:00',
      deleted_at: null,
      is_activated: true,
      image_url: '@/assets/image/avatar/2d.png',
      video_url: '',
      user_id: 490189310428057600,
      pretrain_status: 1,
      preload_status: 2,
      avatar_id: '5rRekX4u0M',
      source_image_url: '',
      name: '职业女性',
      gender: 'woman',
      error_msg: null,
    },
  ];

  const characterLibrary = ref(defaultCharacterLibrary);
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const pretrainList = ref<PretrainItemProps[]>([]); // 预训练列表
  const createModalRef = ref(); // 模态框引用

  // hoverVideoId 用于追踪当前hover的视频avatar_id
  const hoverVideoId = ref<string | null>(null);
  // const digitalList = ref<{ url: string; gender: string }[]>([{ url: '', gender: '' }]);
  // video-modal控制
  const videoModalVisible = ref(false);
  const videoModalUrl = ref('');
  const videoModalName = ref('');
  // const selectedDigitalMan = ref<any>({ index: 0, ...digitalList.value[0] });
  // const digitalHumanLoading = ref(false);

  const personName = ref('');
  const openNameModal = ref(false);
  const currentRenamingItem = ref<PretrainItemProps | null>(null);
  const selectedDigitalHuman = ref<string | null>(null);
  const audioPlayer = ref<HTMLAudioElement | null>(null);

  const deletePopoverOpenId = ref<string | null>(null);
  function openDeletePopover(id) {
    deletePopoverOpenId.value = id;
  }
  function closeDeletePopover() {
    deletePopoverOpenId.value = null;
  }

  interface PretrainItemProps {
    avatar_id: string;
    image_url: string;
    name: string;
    pretrain_status?: number;
    error_msg?: string;
    audio_url?: string;
  }

  // 获取预训练列表
  const getList = async () => {
    try {
      const data = await getPretrainList({
        user_id: userId,
      });
      pretrainList.value = data.list || [];
    } catch (error) {
      console.log('获取列表失败', error);
      message.error('获取列表失败');
    }
  };

  // const queryDigitalHumanList = async () => {
  //   digitalHumanLoading.value = true;
  //   getModeDigitals()
  //     .then((data: any) => {
  //       digitalList.value = data;
  //       selectedDigitalMan.value = { index: 0, ...data[0] };
  //     })
  //     .finally(() => {
  //       digitalHumanLoading.value = false;
  //     });
  // };

  // 获取预训练列表时调用
  onMounted(() => {
    getList();
    // queryDigitalHumanList();
  });

  // 删除数字人
  const handleDelete = async (id: string) => {
    // console.log('删除数字人', id);
    try {
      await deleteMode({
        ids: [id],
      });
      getList();
    } catch (error) {
      console.log('删除数字人失败', error);
    }
  };

  // 重新生成数字人
  const handleReGenerate = async (item: PretrainItemProps) => {
    console.log('重新生成数字人', item);

    // pageLoading.value = true;
    // const isCartoon = selectedDigitalMan.value.index === digitalList.value.length - 1;
    strtPretrain({
      // ...item,
      id: item.id,
      re_pretrain: true,
      source_path: item.source_image_url,
      category: item.category || 'fast',
      is_cartoon: item.is_cartoon || false,
      user_id: item.user_id,
      image_url: item.image_url,
      target_path: item.target_path,
      name: item.name,
      gender: item.gender,
      speaker: item.default_speaker,
      target_path: item.target_path,
    }).then(() => {
      // props.handleCloseModal();
      // isOpenModal.value = false;
      // message.success('重新开始训练');
    });
    // .catch(() => {
    //   message.error('训练失败');
    // })
    // .finally(() => {
    //   pageLoading.value = false;
    // });
    setTimeout(()=> {
      getList();
    }, 1000);
  };

  //reName
  const handleReName = async (item: PretrainItemProps) => {
    if (!personName.value) {
      message.error('请输入');
      return;
    }
    try {
      await reName({ id: item.id, name: personName.value });
      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;
      getList();
    } catch (error) {
      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;
      console.log('重命名失败', error);
    }
  };

  // 打开重命名模态框
  const handleOpenRenameModal = (item: PretrainItemProps) => {
    personName.value = item.name;
    openNameModal.value = true;
    currentRenamingItem.value = item;
  };

  // 处理数字人选择
  const handleDigitalHumanSelect = (item: PretrainItemProps) => {
    // 选中当前数字人
    selectedDigitalHuman.value = item.avatar_id;

    // 播放音频（这里需要根据实际API调整）
    playAudio(item);
  };

  // 播放音频
  const playAudio = (item: PretrainItemProps) => {
    // 停止当前播放的音频
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      audioPlayer.value = null;
    }

    // 创建新的音频播放器
    audioPlayer.value = new Audio();

    // 这里需要根据实际的音频URL来设置
    // 假设音频URL存储在item.audio_url中，如果没有这个字段需要根据实际API调整
    if (item.audio_url) {
      audioPlayer.value.src = item.audio_url;
      audioPlayer.value.play().catch((error) => {
        console.log('音频播放失败:', error);
      });
    } else {
      // 如果没有音频URL，可以播放默认音频或显示提示
      console.log('该数字人暂无音频');
    }
  };

  // 打开创建模态框
  const handleOpenModal = () => {
    console.log('打开模态框', createModalRef.value);
    createModalRef.value?.openModal();
  };

  const handleCloseModal = () => {
    getList();
  };

  const handleVideoModal = (videoUrl: string, name: string) => {
    videoModalUrl.value = videoUrl;
    videoModalName.value = name;
    videoModalVisible.value = true;
  };
  const handleVideoModalClose = () => {
    videoModalVisible.value = false;
    videoModalName.value = '';
    videoModalUrl.value = '';
  };
</script>
<template>
  <div class="digital-image-container">
    <div class="my-digital-person-container">
      <span class="digital-person-title">我的数字人</span>
      <a-button type="primary" class="create-digital-person-btn" @click="handleOpenModal">
        <PlusOutlined />
        <span class="btn-text">创建数字人</span>
      </a-button>
      <template v-if="pretrainList.length > 0">
        <div class="my-digital-person">
        <div v-for="item in pretrainList" :key="item.avatar_id" class="imageBox">
          <a-popover
            :open="openNameModal && currentRenamingItem?.avatar_id === item.avatar_id"
            trigger="click"
            placement="topRight"
            @update:open="
              (visible) => {
                if (!visible) {
                  openNameModal = false;
                  personName.value = '';
                  currentRenamingItem = null;
                }
              }
            "
          >
            <template #content>
              <div style="width: 220px">
                <div style="margin-bottom: 8px">重命名</div>
                <a-input
                  v-model:value="personName"
                  maxlength="10"
                  placeholder="请输入形象名称，10个字内"
                  style="margin-bottom: 8px"
                />
                <div style="text-align: right">
                  <a-button
                    size="small"
                    style="margin-right: 8px"
                    @click="
                      () => {
                        openNameModal = false;
                        personName.value = '';
                        currentRenamingItem = null;
                      }
                    "
                    >取消</a-button
                  >
                  <a-button size="small" type="primary" @click="handleReName(currentRenamingItem!)">确定</a-button>
                </div>
              </div>
            </template>
            <div>
              <template v-if="!item.video_url">
                <img class="img" :src="item.image_url" alt="" :preview="false" />
              </template>
              <template v-else>
                <div
                  class="video-hover-wrapper"
                  @mouseenter="hoverVideoId = item.avatar_id"
                  @mouseleave="hoverVideoId = null"
                >
                  <video
                    class="img"
                    :src="item.video_url"
                    :poster="item.image_url"
                    playsinline
                    controlsList="nodownload noplaybackrate"
                    disablePictureInPicture
                  />
                  <a-popover
                    trigger="hover"
                    placement="top"
                    :open="deletePopoverOpenId === item.avatar_id"
                    @open="openDeletePopover(item.avatar_id)"
                    @close="closeDeletePopover"
                  >
                    <template #content>
                      <div style="width: 220px">
                        <div style="font-size: 16px; color: #17181a; margin-bottom: 8px">
                          确定删除数字人"{{ item.name }}"？
                        </div>
                        <div style="color: #999; margin-bottom: 12px">删除后不可恢复</div>
                        <div style="text-align: right">
                          <a-button size="small" style="margin-right: 8px" @click="closeDeletePopover">取消</a-button>
                          <a-button
                            size="small"
                            type="primary"
                            @click="
                              handleDelete(item.id);
                              closeDeletePopover();
                            "
                            >确定</a-button
                          >
                        </div>
                      </div>
                    </template>
                    <a-dropdown placement="rightTop" class="more-icon">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="1" @click="handleOpenRenameModal(item)">重命名</a-menu-item>
                          <a-menu-item key="2" @click.stop="openDeletePopover(item.avatar_id)">删除</a-menu-item>
                        </a-menu>
                      </template>
                      <a-button style="border: none;">
                        <Icon name="gengduo" :size="20" class="hover-show-icon" />
                      </a-button>
                    </a-dropdown>
                  </a-popover>
                  <Icon
                    v-if="hoverVideoId === item.avatar_id"
                    name="a-bianzu2"
                    :size="32"
                    class="video-hover-icon"
                    style="pointer-events: auto; cursor: pointer"
                    @click.stop="handleVideoModal(item.video_url, item.name)"
                  />
                </div>
              </template>
              <a-tooltip placement="bottom" :title="item.name">
                <span class="roleName">{{ item.name }}</span>
              </a-tooltip>
              <!-- <Loading v-if="item.pretrain_status === 0" :state="item.error_msg || '训练失败'" /> -->
              <Loading
                v-if="item.pretrain_status === 0"
                state="数字人生成失败，请重新生成"
                :name="item.name"
                :handle-re-generate="() => handleReGenerate(item)"
                :handle-delete="() => handleDelete(item.id)"
              />

              <Loading v-else-if="item.pretrain_status === 2" state="正在训练中..." />
            </div>
          </a-popover>
        </div>
      </div>
      </template>
      <template v-else>
        <div class="card-list-empty">
          <img :src="EmptyImage" alt="" class="empty-image" />
          <span class="empty-text">你还没有创建数字人哦，快去创建吧</span>
        </div>
      </template>
    </div>
    <div class="public-digital-person-container">
      <span class="digital-person-title">公共数字人</span>
      <div class="public-digital-person">
        <div v-for="(item, index) in characterLibrary" :key="index" class="imageBox">
          <img class="img" :src="AVATAR01" :preview="false" alt="静态图片" />
          <a-tooltip placement="bottom" title="职场女性">
            <span class="roleName">{{ item.name }}</span>
          </a-tooltip>
        </div>
      </div>
    </div>
    <a-modal
      v-model:open="videoModalVisible"
      :title="videoModalName"
      :width="400"
      :footer="null"
      @cancel="handleVideoModalClose"
    >
      <video v-if="videoModalUrl" :src="videoModalUrl" controls autoplay loop style="width: 100%" />
    </a-modal>
    <CreateDigitalHumanModal ref="createModalRef" :handle-close-modal="handleCloseModal" />
  </div>
</template>

<style lang="less" scoped>
  .digital-image-container {
    padding: 22px 0 0 18px;
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
    height: calc(100vh - 230px);
    overflow-y: auto;
    .my-digital-person-container {
      display: flex;
      flex-direction: column;

      .create-digital-person-btn {
        width: 126px;
        height: 40px;
        background: #1777ff;
        border-radius: 8px;
        margin-bottom: 24px;

        .btn-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }

      .my-digital-person {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
        gap: 20px;

        .imageBox {
          width: 180px;
          height: 238px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          border-radius: 11px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border: 2px solid transparent;

          &:hover {
            background: #FAFAFA;
            box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.15);
            // border: 1px solid #1777ff;
          }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img {
            // padding-left: 20px;
            margin: 0 15px;
            width: 148px;
            height: 224px;
            object-fit: contain;
          }

          .roleName {
            position: absolute;
            bottom: -2px;
            width: 180px;
            height: 38px;
            background: rgba(226, 233, 242, 0.8);
            border-radius: 0px 0px 11px 11px;
            backdrop-filter: blur(5.43435347681893px);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            // margin-left: 6px;
          }
        }
      }
    }

    .card-list-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 60px;

      .empty-text{
        margin-top: 12px;
        width: 224px;
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #636466;
        line-height: 22px;
        text-align: right;
        font-style: normal;
      }
    }

    .public-digital-person-container {
      display: flex;
      flex-direction: column;

      .public-digital-person {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
        gap: 20px;

        .imageBox {
          width: 180px;
          height: 238px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          border-radius: 11px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border: 2px solid transparent;

          // &:hover {
            // border: 1px solid #1777ff;
            // background: #FAFAFA;
            // box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.15);
          // }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img {
            padding-left: 20px;
            width: 148px;
            height: 224px;
            object-fit: contain;
          }

          .roleName {
            position: absolute;
            bottom: -2px;
            width: 180px;
            height: 38px;
            background: rgba(226, 233, 242, 0.8);
            border-radius: 0px 0px 11px 11px;
            backdrop-filter: blur(5.43435347681893px);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            // margin-left: 6px;
          }
        }
      }
    }

    .digital-person-title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #17181a;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      margin-bottom: 12px;
    }
  }

  .video-hover-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  .video-hover-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    pointer-events: auto;
    cursor: pointer;
  }
  .more-icon {
    position: absolute;
    right: 0;
    top: 0;
    border: none;
    z-index: 2;
    background: none;
    pointer-events: auto;
    cursor: pointer;
    border: none !important;
    box-shadow: none !important;
  }
  .more-icon .hover-show-icon {
    padding: 3px 0;
    display: none;
  }
  .more-icon:hover .hover-show-icon {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    background: #F2F8FF;
    display: block;
    border-radius: 2px;
    // opacity: 0.8;
  }

  :deep(.ant-menu-item:hover),
  :deep(.ant-menu-item-active),
  :deep(.ant-dropdown-menu-item:hover),
  :deep(.ant-dropdown-menu-item-active) {
    background: #f2f8ff !important;
  }

  :deep(.ant-popconfirm-message-icon){
    display: none;
  }
</style>
