<template>
  <a-modal
    :open="props.visible"
    title="创建声音"
    width="860px"
    ok-text="开始克隆"
    :confirmLoading="startCloningLoading"
    :okButtonProps="{ disabled: startCloningLoading }"
    @cancel="handleCancel"
    @ok="handleStartCloning"
  >
    <div class="add-sound-modal">
      <!-- 1. 上传或录制音频入口 -->
      <div class="audio-title-section">
        <span style="color: red; margin-right: 2px">*</span>
        <span class="audio-title-label">上传或录制音频</span>
        <ExclamationCircleOutlined class="label-icon" />
        <span class="label-tip">（上传或录制5-30s音频，一键克隆，创建专属声音）</span>
      </div>

      <!-- 2. 建议朗读文案 -->
      <div class="suggest-section" v-if="!uploadVideoUrl">
        <div class="suggest-title">
          <div class="suggest-label">建议朗读以下例句录制音频</div>
          <a-button
            class="change-button"
            type="link"
            @click="changeSuggestText"
            @mouseenter="isChangeBtnHover = true"
            @mouseleave="isChangeBtnHover = false"
          >
            <Icon class="change-icon" :name="isChangeBtnHover ? 'shuaxin' : 'sync'" :size="16" />
            换一个
          </a-button>
        </div>
        <div class="suggest-content">{{ currentSuggestText }}</div>
      </div>


      <div class="audio-upload-section" v-if="!uploadVideoUrl && !recordVisible">
        <div class="upload-box">
          <div class="upload-icon">
            <Icon
              class="upload-inner-icon"
              :name="isUploadBtnHover ? 'shangchuan3' : 'shangchuan'"
              :size="16"
              @mouseenter="isUploadBtnHover = true"
              @mouseleave="isUploadBtnHover = false"
            />
          </div>
          <div class="upload-text">
            <a-upload v-bind="UploadProps">
              <a-button class="upload-button">上传音频</a-button>
            </a-upload>
            <span>支持格式wav，时长5-30s，建议25-130字</span>
          </div>
        </div>
        <div class="divider">
          <div class="divider-line"></div>
          <div class="divider-text">或</div>
          <div class="divider-line"></div>
        </div>
        <div class="record-box">
          <div class="record-icon">
            <Icon
              :name="isRecordBtnHover ? 'luyin2' : 'luyin1'"
              :size="16"
              @mouseenter="isRecordBtnHover = true"
              @mouseleave="isRecordBtnHover = false"
            />
          </div>
          <div class="record-text">
            <a-button class="record-button" @click="handleRecordAudio">录制声音</a-button>
            <span>为了提升声音克隆质量，请避免噪音、杂音、多人对话等情况</span>
          </div>
        </div>
      </div>

      <!-- 上传音频中 -->
      <div class="uploading-section"  v-if="uploadVideoUrl">

        <!-- <div>
          <video :src="uploadVideoUrl" controls></video>
        </div>
        <div class="audio-player-container"> -->
          <!-- 音频播放器 -->
          <!-- <div class="audio-player">
            <div class="play-button" @click="togglePlay">
              <Icon
                :name="isPlaying ? 'zanting' : 'bofang'"
                :size="20"
                color="#1777ff"
              />
            </div>
            <div class="progress-section">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: progressPercent + '%' }"
                ></div>
              </div>
              <div class="time-display">
                {{ formatTime(currentTime) }}/{{ formatTime(duration) }}
              </div>
            </div>
          </div> -->

          <!-- 重新录制按钮 -->
        <div class="re-record-section">
          <video :src="uploadVideoUrl" controls controlsList="nodownload noplaybackrate"></video>
          <a-button class="re-record-button" @click="handleReRecord">
            <Icon name="sync" :size="14" />
            <span class="text">重新录制</span>
          </a-button>
          <div class="quality-tip">
            为了提升声音克隆质量，请避免噪音、杂音、多人对话等情况
          </div>
        </div>
      </div>

      <div class="record-section" v-if="recordVisible">
        <div class="record-icon">
          <Icon
            name="recording"
            size="72"
            :style="{ opacity: isRecordIconHover ? '0.5' : '1' }"
            @click="stopRecordAudio"
            @mouseenter="isRecordIconHover = true"
            @mouseleave="isRecordIconHover = false"
          />
        </div>
        <div class="record-time">
          {{ formatRecordTime(recordTime) }}/{{ formatRecordTime(maxRecordTime) }}
        </div>
        <div class="quality-tip">
          为了提升声音克隆质量，请避免噪音、杂音、多人对话等情况
        </div>
        <!-- <a-button class="stop-record-button" @click="stopRecordAudio">停止录音</a-button> -->
      </div>

    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits, ref, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/index.vue';
  import { UploadVideo, StartCloning } from '@/api/avatarChat';
  import { getLocalItem } from '@/utils/common';
  import { suggestTexts } from './const.ts';
  import { recOpen, recStart, recBlobStop } from '@/utils/recorder';

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');


  const props = defineProps<{ visible: boolean }>();
  const emit = defineEmits(['update:visible', 'refresh-speakers-data']);

  // 关闭弹窗
  const handleCancel = () => {
    emit('update:visible', false);
  };

  const isChangeBtnHover = ref(false);
  const currentSuggestIndex = ref(0);
  const currentSuggestText = ref(suggestTexts[0]);
  // 录音相关
  const recordVisible = ref(false);
  const recordError = ref('');
  const isUploadBtnHover = ref(false);
  const isRecordBtnHover = ref(false);
  const recordContent = ref('');
  const isRecordIconHover = ref(false);

  // 录音时长相关
  const recordTime = ref(0); // 已录制秒数
  const maxRecordTime = 30; // 最大录音时长（秒）
  let recordTimer: any = null;

  const loading = ref(false);
  const uploadVideoUrl = ref('');
  const audioId = ref('');

  const startCloningLoading = ref(false);

  // 格式化时间
  const formatRecordTime = (s: number) => {
    const mm = String(Math.floor(s / 60)).padStart(2, '0');
    const ss = String(s % 60).padStart(2, '0');
    return `${mm}:${ss}`;
  };



  const changeSuggestText = () => {
    let nextIndex = Math.floor(Math.random() * suggestTexts.length);
    // 保证不会和当前一样
    while (nextIndex === currentSuggestIndex.value && suggestTexts.length > 1) {
      nextIndex = Math.floor(Math.random() * suggestTexts.length);
    }
    currentSuggestIndex.value = nextIndex;
    currentSuggestText.value = suggestTexts[nextIndex];
  };


  const UploadProps = {
    beforeUpload: (file: File) => {
      const is = file.type === 'audio/wav' || file.name.endsWith('.wav');
      if (!is) {
        message.error('只支持上传wav格式的音频文件');
        return false;
      }
      // 校验音频时长
      return new Promise((resolve) => {
        const url = URL.createObjectURL(file);
        const audio = new Audio(url);
        audio.addEventListener('loadedmetadata', () => {
          const duration = audio.duration;
          URL.revokeObjectURL(url);
          if (duration < 5 || duration > 30) {
            message.error('音频时长为 5-30s');
            resolve(false);
          } else {
            resolve(true);
          }
        });
      });
    },
    customRequest: async (detail: { file: File }) => {

      const file = detail.file;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('user_id', userId);
      formData.append('ref_text', currentSuggestText.value);
      loading.value = true;
      UploadVideo(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: any) => {
          uploadVideoUrl.value = data.minio_url;
          audioId.value = data.id; // 保存id
          // uploadAvatarUrl.value = data?.[0];
          // isAvatarUploaded.value = data?.[0] ? true : false;
        })
        .catch(() => {
          message.error('上传失败，请上传5-30秒的wav格式音频');
        })
        .finally(() => {
          loading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'audio/wav',
    showUploadList: false,
  };

  const handleReRecord = () => {
    uploadVideoUrl.value = '';
  };

  const handleRecordAudio = async () => {
    recordError.value = '';
    recOpen(
      () => {
        // 权限获取成功，弹窗并开始录音
        console.log('权限获取成功，弹窗并开始录音');
        recordVisible.value = true;
        recordTime.value = 0;
        recordTimer = setInterval(() => {
          recordTime.value++;
          if (recordTime.value > maxRecordTime) {
            message.error('音频时长不符合，请控制在5-30s之内');
            stopRecordAudio(); // 达到最大时长自动停止
          }
        }, 1000);
        recStart();
      },
      (err: any) => {
        // 权限获取失败
        console.error(err || '获取录音权限失败！');
        message.error(err || '获取录音权限失败！');
      },
      (data: any) => {
        recordContent.value = data;
        // 实时录音数据回调（可选，暂时不用处理）
      }
    );
  };

  const stopRecordAudio = () => {
    if (recordTimer) {
      clearInterval(recordTimer);
      recordTimer = null;
    }
    // 录音时长校验
    if (recordTime.value < 5) {
      recordVisible.value = false;
      message.error('音频时长不符合，请控制在5-30s之内');
      return;
    }
    recBlobStop(async (blob: Blob) => {
      recordVisible.value = false;
      const wavBlob = blob.wav_blob;
      const file = new File([wavBlob], 'record.wav', { type: 'audio/wav' });
      file.uid = Date.now() + '_' + Math.random(); // 添加唯一uid
      const formData = new FormData();
      formData.append('file', file);
      formData.append('user_id', userId);
      formData.append('ref_text', currentSuggestText.value);
      formData.append('name', '');
      loading.value = true; // 应为 audio/wav
      try {
        const data = await UploadVideo(formData, {
          headers: {
            'content-type': 'multipart/form-data',
          },
        });
        uploadVideoUrl.value = data.minio_url;
        audioId.value = data.id; // 保存id
      } catch (e) {
        message.error('上传失败，请上传5-30秒的wav格式音频');
      } finally {
        loading.value = false;
      }
    });
  };

  onUnmounted(() => {
    if (recordTimer) clearInterval(recordTimer);
  });


  const handleStartCloning = () => {
    if(!audioId.value && recordVisible.value){
      message.error('音频文件上传或录制未完成，请稍后');
      return;
    }
    if (!audioId.value) {
      message.error('请上传或录制音频文件');
      return;
    }


    startCloningLoading.value = true;
    StartCloning(audioId.value)
      .then(() => {
        message.success('声音克隆中');
        emit('update:visible', false);
        emit('refresh-speakers-data'); // 通知父组件刷新列表
        startCloningLoading.value = false;
      })
      .catch(() => {
        startCloningLoading.value = false;
        message.error('克隆失败，请重试');
      });
  }

</script>

<style scoped lang="less">
  .add-sound-modal {
    border-top: 1px solid #e5e6eb;

    .audio-title-section {
      margin: 15px 0;

      .audio-title-label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #17181a;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      .label-icon {
        margin: 0 4px 0 8px;
        color: #231f20;
        font-size: 12px;
      }

      .label-tip {
        width: 271px;
        height: 17px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #636466;
        line-height: 17px;
        text-align: left;
        font-style: normal;
      }
    }
    .suggest-section {
      margin-bottom: 20px;

      .suggest-title {
        display: flex;
        justify-content: space-between;

        .change-button {
          padding: 0;
          color: #636466;

          .change-icon {
            margin: 0 4px 2px 0;
          }
        }

        &:hover {
          .change-button {
            color: #1777ff;
          }
        }

        .suggest-label {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #636466;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-top: 6px;
        }
      }

      .suggest-content {
        background: #f7f8fa;
        border-radius: 4px;
        padding: 20px;
        font-size: 14px;
        margin: 4px 0;
        min-height: 60px;
      }
    }

    .audio-upload-section {
      display: flex;
      justify-content: space-between;
      height: 150px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px dashed #e6e6e6;
      margin-bottom: 150px;

      .upload-box,
      .record-box {
        flex: 1;
        min-width: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .upload-box {
        .upload-icon {
          width: 40px;
          height: 40px;
          background: #f2f8ff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;
          transition: background 0.2s;

          // &:hover {
          //   background: #1777ff;


          // }
        }

        .upload-text {
          display: flex;
          flex-direction: column;

          .upload-button {
            text-align: left;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
          }

          > span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #969799;
            line-height: 17px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .divider {
        display: flex;
        flex-direction: column;
        margin: 15px 0;
        // margin:  10px 0;

        .divider-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #bbbbbb;
          line-height: 14px;
          text-align: left;
          font-style: normal;
        }

        .divider-line {
          width: 1px;
          height: 100%;
          margin: 4px;
          background: #e5e6eb;
        }
      }

      .record-box {
        .record-icon {
          width: 40px;
          height: 40px;
          background: #f2f8ff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;
        }
        .record-text {
          display: flex;
          flex-direction: column;

          .record-button {
            text-align: left;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
          }

          > span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #969799;
            line-height: 17px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }

    .uploading-section{
      display: flex;
      // height: 150px;
      background: #ffffff;
      border-radius: 8px;
      // border: 1px dashed #e6e6e6;

      .re-record-section{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start; // 让内容靠上
        width: 100%;
        border-radius: 8px;
        border: 1px dashed #e6e6e6;
        padding-top: 0;
        margin-top: 0;

        video {
          width: 80%;
          height: 70px;
          margin: 0; // 去除默认外边距
          display: block; // 避免inline元素带来的间隙
        }

        .re-record-button{
          background: #FFFFFF;
          border-radius: 6px;
          border: 1px solid #E1E3E6;
          margin: 7px 0;


          .text{
            width: 100px;
            height: 36px;
            border-radius: 6px;

          }
        }

        .quality-tip{
          width: 324px;
          height: 17px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #969799;
          line-height: 17px;
          text-align: left;
          font-style: normal;
          margin-bottom: 13px;
        }
      }
    }

    .record-section{
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      border: 1px dashed #e6e6e6;

      .record-icon{
        margin: 21px;
      }

      .record-time {
        font-size: 16px;
        color: #1777ff;
        margin-bottom: 8px;
      }
      .stop-record-button {
        margin-top: 12px;
      }

      .quality-tip{
        width: 324px;
        height: 17px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        text-align: left;
        font-style: normal;
        margin: 8px 0 25px 0;
      }
    }

  }

</style>